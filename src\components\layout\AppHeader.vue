<template>
  <header class="bg-white shadow-sm border-b border-gray-200">
    <div class="flex items-center justify-between px-6 py-4">
      <!-- Left side - Mobile menu button and title -->
      <div class="flex items-center">
        <button
          @click="$emit('toggle-sidebar')"
          class="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
        >
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
        
        <h1 class="ml-4 lg:ml-0 text-xl font-semibold text-gray-900">
          {{ pageTitle }}
        </h1>
      </div>
      
      <!-- Right side - User menu -->
      <div class="flex items-center space-x-4">
        <BaseButton
          variant="outline"
          class="justify-start"
        >
          <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><title>logout</title><path d="M17 7L15.59 8.41L18.17 11H8V13H18.17L15.59 15.58L17 17L22 12M4 5H12V3H4C2.9 3 2 3.9 2 5V19C2 20.1 2.9 21 4 21H12V19H4V5Z" /></svg>
          Logout
        </BaseButton>
      </div>
    </div>
  </header>
</template>

<script>
import { useNotification } from '@/composables/useNotification'
import BaseButton from '@/components/base/BaseButton.vue'

export default {
  name: 'AppHeader',
  emits: ['toggle-sidebar'],
  setup() {
    const { success } = useNotification()
    return { success }
  },
  components: {
    BaseButton
  },
  data() {
    return {
    }
  },
  computed: {
    pageTitle() {
      return this.$route.meta?.title || 'Dashboard'
    }
  },
  mounted() {
  },
  beforeUnmount() {
  },
  methods: {
  }
}
</script>
