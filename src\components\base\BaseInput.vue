<template>
  <div class="space-y-1">
    <label
      v-if="label"
      :for="inputId"
      class="block text-sm font-medium text-gray-700"
    >
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>
    
    <div class="relative">
      <input
        :id="inputId"
        :type="type"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :required="required"
        :class="inputClasses"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
      />
      
      <div
        v-if="$slots.suffix"
        class="absolute inset-y-0 right-0 flex items-center pr-3"
      >
        <slot name="suffix" />
      </div>
    </div>
    
    <p
      v-if="error"
      class="text-sm text-red-600"
    >
      {{ error }}
    </p>
    
    <p
      v-else-if="hint"
      class="text-sm text-gray-500"
    >
      {{ hint }}
    </p>
  </div>
</template>

<script>
export default {
  name: 'BaseInput',
  props: {
    modelValue: {
      type: [String, Number],
      default: ''
    },
    type: {
      type: String,
      default: 'text'
    },
    label: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    required: {
      type: Boolean,
      default: false
    },
    error: {
      type: String,
      default: ''
    },
    hint: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: 'md',
      validator: (value) => ['sm', 'md', 'lg'].includes(value)
    }
  },
  emits: ['update:modelValue', 'blur', 'focus'],
  data() {
    return {
      inputId: `input-${Math.random().toString(36).substring(2, 11)}`
    }
  },
  computed: {
    inputClasses() {
      const baseClasses = [
        'block',
        'w-full',
        'rounded-lg',
        'border',
        'shadow-sm',
        'transition-colors',
        'duration-200',
        'focus:outline-none',
        'focus:ring-2',
        'focus:ring-offset-2',
        'disabled:bg-gray-50',
        'disabled:text-gray-500',
        'disabled:cursor-not-allowed'
      ]

      // Size classes
      const sizeClasses = {
        sm: ['px-3', 'py-1.5', 'text-sm'],
        md: ['px-3', 'py-2', 'text-sm'],
        lg: ['px-4', 'py-3', 'text-base']
      }

      // State classes
      const stateClasses = this.error
        ? [
            'border-red-300',
            'text-red-900',
            'placeholder-red-300',
            'focus:border-red-500',
            'focus:ring-red-500'
          ]
        : [
            'border-gray-300',
            'text-gray-900',
            'placeholder-gray-400',
            'focus:border-primary-500',
            'focus:ring-primary-500'
          ]

      return [
        ...baseClasses,
        ...sizeClasses[this.size],
        ...stateClasses
      ]
    }
  },
  methods: {
    handleInput(event) {
      this.$emit('update:modelValue', event.target.value)
    },
    handleBlur(event) {
      this.$emit('blur', event)
    },
    handleFocus(event) {
      this.$emit('focus', event)
    }
  }
}
</script>
