<template>
  <div class="space-y-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900">List Orders</h1>
      <BaseButton
        variant="primary"
        @click="navigateToCreateOrder"
      >
        <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        Create Order
      </BaseButton>
    </div>

    <!-- Filters -->
    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <BaseInput
          v-model="filters.orderId"
          label="Order ID"
          placeholder="Search by Order ID"
        />

        <BaseSelect
          v-model="filters.status"
          label="Status"
          placeholder="All Status"
          :options="statusOptions"
        />

        <BaseInput
          v-model="filters.orderDate"
          label="Order Date"
          type="date"
        />
      </div>
      
      <div class="flex space-x-3">
        <BaseButton
          variant="primary"
          @click="applyFilters"
        >
          Apply
        </BaseButton>
        
        <BaseButton
          variant="secondary"
          @click="resetFilters"
        >
          Reset
        </BaseButton>
      </div>
    </div>

    <!-- Orders Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div v-if="loading" class="p-8 text-center">
        <loading-spinner :message="'Loading orders...'" />
      </div>
      
      <div v-else-if="error" class="p-8 text-center">
        <p class="text-red-600">{{ error }}</p>
        <BaseButton
          variant="primary"
          class="mt-4"
          @click="fetchOrders"
        >
          Retry
        </BaseButton>
      </div>
      
      <div v-else>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Order ID
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Order Date
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr
                v-for="order in filteredOrders"
                :key="order.orderId"
                class="hover:bg-gray-50 transition-colors duration-200"
              >
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    :class="[
                      'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                      getStatusBadgeClass(order.status)
                    ]"
                  >
                    {{ order.status }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {{ order.orderId }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ order.orderDate }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {{ formatCurrency(order.total) }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <div v-if="filteredOrders.length === 0" class="p-8 text-center">
          <p class="text-gray-500">No orders found matching your criteria.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useOrdersStore } from '@/stores/orders'
import { formatCurrency, getStatusBadgeClass } from '@/utils/formatters'
import BaseButton from '@/components/base/BaseButton.vue'
import BaseInput from '@/components/base/BaseInput.vue'
import BaseSelect from '@/components/base/BaseSelect.vue'
import LoadingSpinner from '@/components/ui/LoadingSpinner.vue'

export default {
  name: 'OrderListView',
  components: {
    BaseButton,
    BaseInput,
    BaseSelect,
    LoadingSpinner
  },
  setup() {
    const ordersStore = useOrdersStore()
    return {
      ordersStore,
      formatCurrency,
      getStatusBadgeClass
    }
  },
  data() {
    return {
      filters: {
        orderId: '',
        status: '',
        orderDate: ''
      },
      appliedFilters: {
        orderId: '',
        status: '',
        orderDate: ''
      },
      statusOptions: [
        { value: '', label: 'All Status' },
        { value: 'Waiting for Payment', label: 'Waiting for Payment' },
        { value: 'On Payment Verification', label: 'On Payment Verification' },
        { value: 'On Release Progress', label: 'On Release Progress' },
        { value: 'Payment Rejected', label: 'Payment Rejected' },
        { value: 'Done', label: 'Done' }
      ]
    }
  },
  computed: {
    orders() {
      return this.ordersStore.orders
    },
    loading() {
      return this.ordersStore.loading
    },
    error() {
      return this.ordersStore.error
    },
    filteredOrders() {
      return this.orders.filter(order => {
        const matchesOrderId = !this.appliedFilters.orderId ||
          order.orderId.toLowerCase().includes(this.appliedFilters.orderId.toLowerCase())

        const matchesStatus = !this.appliedFilters.status || order.status === this.appliedFilters.status

        let matchesDate = true
        if (this.appliedFilters.orderDate) {
          try {
            // Convert order date from "28 Jun 2024, 10:30" format to comparable date
            const orderDateStr = order.orderDate
            const orderDateParts = orderDateStr.split(', ')[0] // Get "28 Jun 2024" part
            const orderDate = new Date(orderDateParts)
            const filterDate = new Date(this.appliedFilters.orderDate)

            // Compare only the date part (ignore time)
            if (!isNaN(orderDate.getTime()) && !isNaN(filterDate.getTime())) {
              matchesDate = orderDate.toDateString() === filterDate.toDateString()
            } else {
              matchesDate = false
            }
          } catch (error) {
            console.error('Date parsing error:', error)
            matchesDate = false
          }
        }

        return matchesOrderId && matchesStatus && matchesDate
      })
    }
  },
  async mounted() {
    await this.fetchOrders()
  },
  methods: {
    async fetchOrders() {
      await this.ordersStore.fetchOrders()
    },
    applyFilters() {
      // Copy current filters to applied filters to trigger filtering
      this.appliedFilters = { ...this.filters }
    },
    resetFilters() {
      this.filters = {
        orderId: '',
        status: '',
        orderDate: ''
      }
      this.appliedFilters = {
        orderId: '',
        status: '',
        orderDate: ''
      }
    },
    navigateToCreateOrder() {
      this.$router.push('/orders/create')
    }
  }
}
</script>
