<template>
  <div class="space-y-6">
    <!-- <PERSON>er -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
      <p class="text-gray-600">Welcome to Superindo Order Management System</p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="h-8 w-8 bg-secondary-100 rounded-lg flex items-center justify-center">
              <svg class="h-5 w-5 text-secondary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Total Orders</p>
            <p class="text-2xl font-semibold text-gray-900">{{ totalOrders }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="h-8 w-8 bg-yellow-100 rounded-lg flex items-center justify-center">
              <svg class="h-5 w-5 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Pending Orders</p>
            <p class="text-2xl font-semibold text-gray-900">{{ pendingOrders }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="h-8 w-8 bg-green-100 rounded-lg flex items-center justify-center">
              <svg class="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Completed Orders</p>
            <p class="text-2xl font-semibold text-gray-900">{{ completedOrders }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="h-8 w-8 bg-primary-100 rounded-lg flex items-center justify-center">
              <svg class="h-5 w-5 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Total Vouchers</p>
            <p class="text-2xl font-semibold text-gray-900">{{ totalVouchers }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <BaseButton
          variant="primary"
          class="justify-start"
          @click="$router.push('/orders/create')"
        >
          <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          Create New Order
        </BaseButton>
        
        <BaseButton
          variant="outline"
          class="justify-start"
          @click="$router.push('/orders')"
        >
          <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          View All Orders
        </BaseButton>
        
        <BaseButton
          variant="outline"
          class="justify-start"
          @click="$router.push('/voucher-management')"
        >
          <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
          </svg>
          Manage Vouchers
        </BaseButton>
      </div>
    </div>

    <!-- Recent Orders -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">Recent Orders</h2>
      </div>
      <div class="p-6">
        <div v-if="loading" class="text-center py-4">
          <loading-spinner :message="'Loading orders...'" />
        </div>
        <div v-else-if="recentOrders.length === 0" class="text-center py-4">
          <p class="text-gray-500">No recent orders found.</p>
        </div>
        <div v-else class="space-y-3">
          <div
            v-for="order in recentOrders"
            :key="order.orderId"
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
          >
            <div>
              <p class="font-medium text-gray-900">{{ order.orderId }}</p>
              <p class="text-sm text-gray-500">{{ order.orderDate }}</p>
            </div>
            <div class="text-right">
              <p class="font-medium text-gray-900">{{ formatCurrency(order.total) }}</p>
              <span
                :class="[
                  'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                  getStatusBadgeClass(order.status)
                ]"
              >
                {{ order.status }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useOrdersStore } from '@/stores/orders'
import { useVouchersStore } from '@/stores/vouchers'
import { formatCurrency, getStatusBadgeClass } from '@/utils/formatters'
import BaseButton from '@/components/base/BaseButton.vue'
import LoadingSpinner from '@/components/ui/LoadingSpinner.vue'

export default {
  name: 'DashboardView',
  components: {
    BaseButton,
    LoadingSpinner
  },
  setup() {
    const ordersStore = useOrdersStore()
    const vouchersStore = useVouchersStore()
    
    return {
      ordersStore,
      vouchersStore,
      formatCurrency,
      getStatusBadgeClass
    }
  },
  computed: {
    orders() {
      return this.ordersStore.orders
    },
    loading() {
      return this.ordersStore.loading
    },
    totalOrders() {
      return this.ordersStore.totalOrders
    },
    pendingOrders() {
      return this.orders.filter(order => 
        ['Waiting for Payment', 'On Payment Verification', 'On Release Progress'].includes(order.status)
      ).length
    },
    completedOrders() {
      return this.orders.filter(order => order.status === 'Done').length
    },
    totalVouchers() {
      return this.vouchersStore.totalVouchers
    },
    recentOrders() {
      return this.orders.slice(0, 5)
    }
  },
  async mounted() {
    await Promise.all([
      this.ordersStore.fetchOrders(),
      this.vouchersStore.fetchVouchers()
    ])
  }
}
</script>
