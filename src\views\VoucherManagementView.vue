<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Voucher Management</h1>
      <p class="text-gray-600">Manage digital vouchers for Superindo</p>
    </div>

    <!-- Vouchers Grid -->
    <div v-if="loading" class="text-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
      <loading-spinner :message="'Loading vouchers...'" />
    </div>
    
    <div v-else-if="error" class="text-center py-8">
      <p class="text-red-600">{{ error }}</p>
      <BaseButton
        variant="primary"
        class="mt-4"
        @click="fetchVouchers"
      >
        Retry
      </BaseButton>
    </div>
    
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div
        v-for="voucher in vouchers"
        :key="voucher.id"
        class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200"
      >
        <!-- Voucher Image Placeholder -->
        <div class="h-32 bg-gradient-to-r from-primary-500 to-primary-600 flex items-center justify-center">
          <div class="text-white text-center">
            <svg class="h-12 w-12 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
            </svg>
            <p class="text-sm font-medium">Digital Voucher</p>
          </div>
        </div>
        
        <!-- Voucher Details -->
        <div class="p-4">
          <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ voucher.name }}</h3>
          <p class="text-sm text-gray-600 mb-3">{{ voucher.description }}</p>
          
          <div class="space-y-2">
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-500">Denomination:</span>
              <span class="font-medium text-gray-900">{{ formatCurrency(voucher.denomination) }}</span>
            </div>
            
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-500">Code:</span>
              <span class="font-medium text-gray-900">{{ voucher.code }}</span>
            </div>
            
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-500">Max Quantity:</span>
              <span class="font-medium text-gray-900">{{ voucher.maxQuantity }}</span>
            </div>
            
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-500">Status:</span>
              <span
                :class="[
                  'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                  voucher.isActive 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                ]"
              >
                {{ voucher.isActive ? 'Active' : 'Inactive' }}
              </span>
            </div>
          </div>
        </div>
        
        <!-- Actions -->
        <div class="px-4 py-3 bg-gray-50 border-t border-gray-200">
          <div class="flex space-x-2">
            <BaseButton
              variant="outline"
              size="sm"
              class="flex-1"
              @click="editVoucher(voucher)"
            >
              Edit
            </BaseButton>
            <BaseButton
              :variant="voucher.isActive ? 'danger' : 'success'"
              size="sm"
              class="flex-1"
              @click="toggleVoucherStatus(voucher)"
            >
              {{ voucher.isActive ? 'Deactivate' : 'Activate' }}
            </BaseButton>
          </div>
        </div>
      </div>
    </div>
    
    <div v-if="!loading && vouchers.length === 0" class="text-center py-8">
      <p class="text-gray-500">No vouchers found.</p>
    </div>
  </div>
</template>

<script>
import { useVouchersStore } from '@/stores/vouchers'
import { useNotification } from '@/composables/useNotification'
import { formatCurrency } from '@/utils/formatters'
import BaseButton from '@/components/base/BaseButton.vue'
import LoadingSpinner from '@/components/ui/LoadingSpinner.vue'

export default {
  name: 'VoucherManagementView',
  components: {
    BaseButton,
    LoadingSpinner
  },
  setup() {
    const vouchersStore = useVouchersStore()
    const { success, info } = useNotification()
    
    return {
      vouchersStore,
      success,
      info,
      formatCurrency
    }
  },
  computed: {
    vouchers() {
      return this.vouchersStore.vouchers
    },
    loading() {
      return this.vouchersStore.loading
    },
    error() {
      return this.vouchersStore.error
    }
  },
  async mounted() {
    await this.fetchVouchers()
  },
  methods: {
    async fetchVouchers() {
      await this.vouchersStore.fetchVouchers()
    },
    editVoucher(voucher) {
      this.info(`Fitur ${voucher.name} akan segara hadir`)
    },
    toggleVoucherStatus(voucher) {
      const action = voucher.isActive ? 'deactivated' : 'activated'
      this.success(`${voucher.name} has been ${action}`)
      // In a real app, you would update the voucher status here
    }
  }
}
</script>
