import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/dashboard'
    },
    {
      path: '/dashboard',
      name: 'Dashboard',
      component: () => import('@/views/DashboardView.vue'),
      meta: {
        title: 'Dashboard'
      }
    },
    {
      path: '/orders',
      name: 'Orders',
      component: () => import('@/views/OrderListView.vue'),
      meta: {
        title: 'Order List'
      }
    },
    {
      path: '/orders/create',
      name: 'CreateOrder',
      component: () => import('@/views/CreateOrderView.vue'),
      meta: {
        title: 'Create Order'
      }
    },
    {
      path: '/voucher-management',
      name: 'VoucherManagement',
      component: () => import('@/views/VoucherManagementView.vue'),
      meta: {
        title: 'Voucher Management'
      }
    }
  ]
})

// Navigation guard to set page title
router.beforeEach((to, from, next) => {
  document.title = to.meta.title ? `${to.meta.title} - Superindo` : 'Superindo'
  next()
})

export default router
