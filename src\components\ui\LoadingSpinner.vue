<template>
  <div :class="containerClasses">
    <div :class="spinnerClasses">
      <div class="animate-spin rounded-full border-b-2" :class="borderClasses"></div>
    </div>
    <p v-if="message" :class="messageClasses">{{ message }}</p>
  </div>
</template>

<script>
export default {
  name: 'LoadingSpinner',
  props: {
    size: {
      type: String,
      default: 'md',
      validator: (value) => ['sm', 'md', 'lg', 'xl'].includes(value)
    },
    message: {
      type: String,
      default: ''
    },
    centered: {
      type: Boolean,
      default: true
    },
    overlay: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    containerClasses() {
      const baseClasses = []
      
      if (this.centered) {
        baseClasses.push('flex', 'flex-col', 'items-center', 'justify-center')
      }
      
      if (this.overlay) {
        baseClasses.push('fixed', 'inset-0', 'bg-white', 'bg-opacity-75', 'z-50')
      }
      
      return baseClasses
    },
    spinnerClasses() {
      const sizeClasses = {
        sm: ['h-4', 'w-4'],
        md: ['h-8', 'w-8'],
        lg: ['h-12', 'w-12'],
        xl: ['h-16', 'w-16']
      }
      
      return sizeClasses[this.size]
    },
    borderClasses() {
      const sizeClasses = {
        sm: ['h-4', 'w-4', 'border-primary-600'],
        md: ['h-8', 'w-8', 'border-primary-600'],
        lg: ['h-12', 'w-12', 'border-primary-600'],
        xl: ['h-16', 'w-16', 'border-primary-600']
      }
      
      return sizeClasses[this.size]
    },
    messageClasses() {
      const sizeClasses = {
        sm: ['mt-1', 'text-xs', 'text-gray-500'],
        md: ['mt-2', 'text-sm', 'text-gray-500'],
        lg: ['mt-3', 'text-base', 'text-gray-600'],
        xl: ['mt-4', 'text-lg', 'text-gray-600']
      }
      
      return sizeClasses[this.size]
    }
  }
}
</script>
