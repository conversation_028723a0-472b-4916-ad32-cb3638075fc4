import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useVouchersStore = defineStore('vouchers', () => {
  // State
  const vouchers = ref([])
  const selectedVouchers = ref([])
  const loading = ref(false)
  const error = ref(null)

  // Getters
  const totalVouchers = computed(() => vouchers.value.length)
  
  const orderSummary = computed(() => {
    return selectedVouchers.value.map(item => {
      const voucher = vouchers.value.find(v => v.id === item.voucherId)
      if (!voucher) return null
      
      return {
        id: voucher.id,
        name: voucher.name,
        denomination: voucher.denomination,
        quantity: item.quantity,
        totalPrice: voucher.denomination * item.quantity
      }
    }).filter(Boolean)
  })

  const grandTotal = computed(() => {
    return orderSummary.value.reduce((total, item) => total + item.totalPrice, 0)
  })

  const hasSelectedVouchers = computed(() => {
    return selectedVouchers.value.some(item => item.quantity > 0)
  })

  // Actions
  const fetchVouchers = async () => {
    loading.value = true
    error.value = null
    
    try {
      // Simulate API call - in real app, this would be an HTTP request
      const response = await import('@/data/voucher_list.json')
      vouchers.value = response.default || response
    } catch (err) {
      error.value = 'Failed to fetch vouchers'
      console.error('Error fetching vouchers:', err)
    } finally {
      loading.value = false
    }
  }

  const updateVoucherQuantity = (voucherId, quantity) => {
    const existingIndex = selectedVouchers.value.findIndex(
      item => item.voucherId === voucherId
    )

    if (quantity <= 0) {
      // Remove voucher if quantity is 0 or negative
      if (existingIndex !== -1) {
        selectedVouchers.value.splice(existingIndex, 1)
      }
    } else {
      // Add or update voucher quantity
      if (existingIndex !== -1) {
        selectedVouchers.value[existingIndex].quantity = quantity
      } else {
        selectedVouchers.value.push({
          voucherId,
          quantity
        })
      }
    }
  }

  const increaseQuantity = (voucherId) => {
    const currentItem = selectedVouchers.value.find(
      item => item.voucherId === voucherId
    )
    const currentQuantity = currentItem ? currentItem.quantity : 0
    updateVoucherQuantity(voucherId, currentQuantity + 1)
  }

  const decreaseQuantity = (voucherId) => {
    const currentItem = selectedVouchers.value.find(
      item => item.voucherId === voucherId
    )
    const currentQuantity = currentItem ? currentItem.quantity : 0
    if (currentQuantity > 0) {
      updateVoucherQuantity(voucherId, currentQuantity - 1)
    }
  }

  const getVoucherQuantity = (voucherId) => {
    const item = selectedVouchers.value.find(
      item => item.voucherId === voucherId
    )
    return item ? item.quantity : 0
  }

  const resetSelection = () => {
    selectedVouchers.value = []
  }

  const clearError = () => {
    error.value = null
  }

  const createOrderData = () => {
    if (!hasSelectedVouchers.value) {
      throw new Error('No vouchers selected')
    }

    return {
      vouchers: orderSummary.value,
      total: grandTotal.value,
      createdAt: new Date().toISOString()
    }
  }

  return {
    // State
    vouchers,
    selectedVouchers,
    loading,
    error,
    
    // Getters
    totalVouchers,
    orderSummary,
    grandTotal,
    hasSelectedVouchers,
    
    // Actions
    fetchVouchers,
    updateVoucherQuantity,
    increaseQuantity,
    decreaseQuantity,
    getVoucherQuantity,
    resetSelection,
    clearError,
    createOrderData
  }
})
