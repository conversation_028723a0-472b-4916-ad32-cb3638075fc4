<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 sticky top-6">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200">
      <h2 class="text-lg font-semibold text-gray-900">Order Summary</h2>
    </div>

    <!-- Content -->
    <div class="p-6">
      <div v-if="orderSummary.length === 0" class="text-center py-8">
        <svg class="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
        </svg>
        <p class="text-gray-500">No vouchers selected</p>
        <p class="text-sm text-gray-400 mt-1">Select vouchers from the list to see your order summary</p>
      </div>
      
      <div v-else class="space-y-4">
        <!-- Selected Vouchers -->
        <div class="space-y-3">
          <div
            v-for="item in orderSummary"
            :key="item.id"
            class="flex justify-between items-start p-3 bg-gray-50 rounded-lg"
          >
            <div class="flex-1 min-w-0">
              <h4 class="text-sm font-medium text-gray-900 truncate">{{ item.name }}</h4>
              <p class="text-xs text-gray-500">{{ formatCurrency(item.denomination) }} x {{ item.quantity }}</p>
            </div>
            <div class="text-right ml-3">
              <p class="text-sm font-medium text-gray-900">{{ formatCurrency(item.totalPrice) }}</p>
            </div>
          </div>
        </div>
        
        <!-- Divider -->
        <hr class="border-gray-200">
        
        <!-- Total -->
        <div class="flex justify-between items-center">
          <span class="text-lg font-semibold text-gray-900">Total</span>
          <span class="text-xl font-bold text-primary-600">{{ formatCurrency(grandTotal) }}</span>
        </div>
        
        <!-- Place Order Button -->
        <BaseButton
          variant="primary"
          size="lg"
          class="w-full"
          :loading="loading"
          :disabled="!hasSelectedVouchers || loading"
          @click="handlePlaceOrder"
        >
          <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
          </svg>
          Place Order
        </BaseButton>
      </div>
    </div>
    
    <!-- Order Details (when vouchers are selected) -->
    <div v-if="orderSummary.length > 0" class="px-6 py-4 bg-gray-50 border-t border-gray-200">
      <div class="text-xs text-gray-500 space-y-1">
        <div class="flex justify-between">
          <span>Total Items:</span>
          <span>{{ totalItems }}</span>
        </div>
        <div class="flex justify-between">
          <span>Total Vouchers:</span>
          <span>{{ orderSummary.length }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useVouchersStore } from '@/stores/vouchers'
import { useOrdersStore } from '@/stores/orders'
import { formatCurrency } from '@/utils/formatters'
import BaseButton from '@/components/base/BaseButton.vue'

export default {
  name: 'OrderSummary',
  components: {
    BaseButton
  },
  emits: ['place-order'],
  setup() {
    const vouchersStore = useVouchersStore()
    const ordersStore = useOrdersStore()
    
    return {
      vouchersStore,
      ordersStore,
      formatCurrency
    }
  },
  computed: {
    orderSummary() {
      return this.vouchersStore.orderSummary
    },
    grandTotal() {
      return this.vouchersStore.grandTotal
    },
    hasSelectedVouchers() {
      return this.vouchersStore.hasSelectedVouchers
    },
    loading() {
      return this.ordersStore.loading
    },
    totalItems() {
      return this.orderSummary.reduce((total, item) => total + item.quantity, 0)
    }
  },
  methods: {
    handlePlaceOrder() {
      this.$emit('place-order')
    }
  }
}
</script>
