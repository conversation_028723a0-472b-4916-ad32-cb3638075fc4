<template>
  <!-- Mobile sidebar overlay -->
  <div
    v-show="isOpen"
    class="fixed inset-0 z-40 lg:hidden"
    @click="$emit('close')"
  >
    <div class="absolute inset-0 bg-gray-600 opacity-75"></div>
  </div>

  <!-- Sidebar -->
  <div
    :class="[
      'fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0',
      isOpen ? 'translate-x-0' : '-translate-x-full'
    ]"
  >
    <!-- Logo -->
    <div class="flex items-center justify-center h-16 px-4 bg-primary-600">
      <div class="flex items-center">
        <div class="h-8 w-8 bg-white rounded-lg flex items-center justify-center">
          <img src="@/assets/superindo-logo.png" alt="Logo" class="h-6 w-6">
        </div>
        <span class="ml-2 text-white font-semibold text-lg">Superindo</span>
      </div>
    </div>

    <!-- Navigation -->
    <nav class="mt-8">
      <div class="px-4 space-y-2">
        <router-link
          v-for="item in navigationItems"
          :key="item.name"
          :to="item.to"
          :class="[
            'group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200',
            isActiveRoute(item.to)
              ? 'bg-primary-100 text-primary-700 border-[1px] border-primary-600'
              : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
          ]"
          @click="$emit('close')"
        >
          <component
            :is="item.icon"
            :class="[
              'mr-3 h-5 w-5 transition-colors duration-200',
              isActiveRoute(item.to)
                ? 'text-primary-600'
                : 'text-gray-400 group-hover:text-gray-600'
            ]"
          />
          {{ item.name }}
        </router-link>
      </div>
    </nav>

    <!-- Footer -->
    <div class="absolute bottom-0 left-0 right-0 p-4">
      <div class="text-xs text-gray-500 text-center">
        © 2025 Superindo
      </div>
    </div>
  </div>
</template>

<script>
// Import icons (you can use any icon library or create custom SVG components)
const DashboardIcon = {
  template: `
    <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z" />
    </svg>
  `
}

const OrdersIcon = {
  template: `
    <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
    </svg>
  `
}

const VoucherIcon = {
  template: `
    <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
    </svg>
  `
}

export default {
  name: 'AppSidebar',
  components: {
    DashboardIcon,
    OrdersIcon,
    VoucherIcon
  },
  props: {
    isOpen: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close'],
  data() {
    return {
      navigationItems: [
        {
          name: 'Dashboard',
          to: '/dashboard',
          icon: 'DashboardIcon'
        },
        {
          name: 'Orders',
          to: '/orders',
          icon: 'OrdersIcon'
        },
        {
          name: 'Voucher Management',
          to: '/voucher-management',
          icon: 'VoucherIcon'
        }
      ]
    }
  },
  methods: {
    isActiveRoute(route) {
      if (route === '/dashboard' && this.$route.path === '/') {
        return true
      }
      return this.$route.path.startsWith(route)
    }
  }
}
</script>
