import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useOrdersStore = defineStore('orders', () => {
  // State
  const orders = ref([])
  const loading = ref(false)
  const error = ref(null)

  // Getters
  const totalOrders = computed(() => orders.value.length)
  
  const ordersByStatus = computed(() => {
    return orders.value.reduce((acc, order) => {
      acc[order.status] = (acc[order.status] || 0) + 1
      return acc
    }, {})
  })

  const filteredOrders = computed(() => {
    return (filters) => {
      return orders.value.filter(order => {
        const matchesOrderId = !filters.orderId || 
          order.orderId.toLowerCase().includes(filters.orderId.toLowerCase())
        
        const matchesStatus = !filters.status || order.status === filters.status
        
        const matchesDate = !filters.orderDate || 
          order.orderDate.includes(filters.orderDate)
        
        return matchesOrderId && matchesStatus && matchesDate
      })
    }
  })

  // Actions
  const fetchOrders = async () => {
    loading.value = true
    error.value = null
    
    try {
      // Simulate API call - in real app, this would be an HTTP request
      const response = await import('@/data/order_list.json')
      orders.value = response.default || response
    } catch (err) {
      error.value = 'Failed to fetch orders'
      console.error('Error fetching orders:', err)
    } finally {
      loading.value = false
    }
  }

  const addOrder = async (orderData) => {
    loading.value = true
    error.value = null
    
    try {
      const newOrder = {
        ...orderData,
        orderId: generateOrderId(),
        orderDate: new Date().toLocaleString('en-GB', {
          day: '2-digit',
          month: 'short',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }),
        status: 'Waiting for Payment'
      }
      
      orders.value.unshift(newOrder)
      
      // In a real application, you would save to backend/file here
      await saveOrdersToFile()
      
      return newOrder
    } catch (err) {
      error.value = 'Failed to create order'
      console.error('Error creating order:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateOrderStatus = async (orderId, newStatus) => {
    const orderIndex = orders.value.findIndex(order => order.orderId === orderId)
    if (orderIndex !== -1) {
      orders.value[orderIndex].status = newStatus
      await saveOrdersToFile()
    }
  }

  // Helper functions
  const generateOrderId = () => {
    const prefix = 'BLK'
    const timestamp = Date.now().toString().slice(-8)
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
    return `${prefix}${timestamp}${random}`
  }

  const saveOrdersToFile = async () => {
    // In a real application, this would save to backend
    // Simulate file save operation
    return new Promise(resolve => {
      setTimeout(() => {
        localStorage.setItem('superindo_orders', JSON.stringify(orders.value))
        resolve()
      }, 100)
    })
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    orders,
    loading,
    error,
    
    // Getters
    totalOrders,
    ordersByStatus,
    filteredOrders,
    
    // Actions
    fetchOrders,
    addOrder,
    updateOrderStatus,
    clearError
  }
})
