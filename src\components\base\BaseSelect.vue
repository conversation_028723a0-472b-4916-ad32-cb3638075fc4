<template>
  <div class="space-y-1">
    <label
      v-if="label"
      :for="selectId"
      class="block text-sm font-medium text-gray-700"
    >
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>
    
    <select
      :id="selectId"
      :value="modelValue"
      :disabled="disabled"
      :required="required"
      :class="selectClasses"
      @change="handleChange"
      @blur="handleBlur"
      @focus="handleFocus"
    >
      <option v-if="placeholder" value="">{{ placeholder }}</option>
      <option
        v-for="option in normalizedOptions"
        :key="option.value"
        :value="option.value"
      >
        {{ option.label }}
      </option>
    </select>
    
    <p
      v-if="error"
      class="text-sm text-red-600"
    >
      {{ error }}
    </p>
    
    <p
      v-else-if="hint"
      class="text-sm text-gray-500"
    >
      {{ hint }}
    </p>
  </div>
</template>

<script>
export default {
  name: 'BaseSelect',
  props: {
    modelValue: {
      type: [String, Number],
      default: ''
    },
    label: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    options: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    required: {
      type: Boolean,
      default: false
    },
    error: {
      type: String,
      default: ''
    },
    hint: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: 'md',
      validator: (value) => ['sm', 'md', 'lg'].includes(value)
    }
  },
  emits: ['update:modelValue', 'blur', 'focus'],
  data() {
    return {
      selectId: `select-${Math.random().toString(36).substring(2, 11)}`
    }
  },
  computed: {
    normalizedOptions() {
      return this.options.map(option => {
        if (typeof option === 'string') {
          return { value: option, label: option }
        }
        return option
      })
    },
    selectClasses() {
      const baseClasses = [
        'block',
        'w-full',
        'rounded-lg',
        'border',
        'shadow-sm',
        'transition-colors',
        'duration-200',
        'focus:outline-none',
        'focus:ring-2',
        'focus:ring-offset-2',
        'disabled:bg-gray-50',
        'disabled:text-gray-500',
        'disabled:cursor-not-allowed',
        'bg-white'
      ]

      // Size classes
      const sizeClasses = {
        sm: ['px-3', 'py-1.5', 'text-sm'],
        md: ['px-3', 'py-2', 'text-sm'],
        lg: ['px-4', 'py-3', 'text-base']
      }

      // State classes
      const stateClasses = this.error
        ? [
            'border-red-300',
            'text-red-900',
            'focus:border-red-500',
            'focus:ring-red-500'
          ]
        : [
            'border-gray-300',
            'text-gray-900',
            'focus:border-primary-500',
            'focus:ring-primary-500'
          ]

      return [
        ...baseClasses,
        ...sizeClasses[this.size],
        ...stateClasses
      ]
    }
  },
  methods: {
    handleChange(event) {
      this.$emit('update:modelValue', event.target.value)
    },
    handleBlur(event) {
      this.$emit('blur', event)
    },
    handleFocus(event) {
      this.$emit('focus', event)
    }
  }
}
</script>
