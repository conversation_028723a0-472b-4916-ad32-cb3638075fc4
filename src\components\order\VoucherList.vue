<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
      <h2 class="text-lg font-semibold text-gray-900">List Voucher Digital</h2>
      <BaseButton
        variant="secondary"
        size="sm"
        @click="resetSelections"
      >
        Reset
      </BaseButton>
    </div>

    <!-- Content -->
    <div class="p-6">
      <div v-if="loading" class="text-center py-8">
        <loading-spinner :message="'Loading vouchers...'" />
      </div>
      
      <div v-else-if="error" class="text-center py-8">
        <p class="text-red-600">{{ error }}</p>
        <BaseButton
          variant="primary"
          class="mt-4"
          @click="fetchVouchers"
        >
          Retry
        </BaseButton>
      </div>
      
      <div v-else class="space-y-4">
        <div
          v-for="voucher in vouchers"
          :key="voucher.id"
          class="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg hover:border-primary-300 transition-colors duration-200"
        >
          <!-- Voucher Image Placeholder -->
          <div class="flex-shrink-0">
            <div class="h-16 w-16 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
              <svg class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
              </svg>
            </div>
          </div>
          
          <!-- Voucher Details -->
          <div class="flex-1 min-w-0">
            <h3 class="text-lg font-semibold text-gray-900">{{ voucher.name }}</h3>
            <p class="text-sm text-gray-600">Denom: {{ formatCurrency(voucher.denomination) }}</p>
            <p class="text-sm text-gray-500">Code: {{ voucher.code }}</p>
          </div>
          
          <!-- Quantity Controls -->
          <div class="flex items-center space-x-3">
            <!-- Decrease Button -->
            <button
              @click="decreaseQuantity(voucher.id)"
              :disabled="getQuantity(voucher.id) === 0"
              class="h-8 w-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              <svg class="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
              </svg>
            </button>
            
            <!-- Quantity Input -->
            <div class="w-16">
              <input
                :value="getQuantity(voucher.id)"
                @input="updateQuantity(voucher.id, $event.target.value)"
                @blur="validateQuantity(voucher.id, $event.target.value)"
                type="number"
                min="0"
                :max="voucher.maxQuantity"
                class="w-full text-center border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
            
            <!-- Increase Button -->
            <button
              @click="increaseQuantity(voucher.id)"
              :disabled="getQuantity(voucher.id) >= voucher.maxQuantity"
              class="h-8 w-8 rounded-full bg-primary-600 text-white flex items-center justify-center hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
            </button>
          </div>
        </div>
        
        <div v-if="vouchers.length === 0" class="text-center py-8">
          <p class="text-gray-500">No vouchers available.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useVouchersStore } from '@/stores/vouchers'
import { useNotification } from '@/composables/useNotification'
import { formatCurrency } from '@/utils/formatters'
import BaseButton from '@/components/base/BaseButton.vue'
import LoadingSpinner from '@/components/ui/LoadingSpinner.vue'

export default {
  name: 'VoucherList',
  components: {
    BaseButton,
    LoadingSpinner
  },
  setup() {
    const vouchersStore = useVouchersStore()
    const { warning } = useNotification()
    
    return {
      vouchersStore,
      warning,
      formatCurrency
    }
  },
  computed: {
    vouchers() {
      return this.vouchersStore.vouchers.filter(voucher => voucher.isActive)
    },
    loading() {
      return this.vouchersStore.loading
    },
    error() {
      return this.vouchersStore.error
    }
  },
  methods: {
    async fetchVouchers() {
      await this.vouchersStore.fetchVouchers()
    },
    getQuantity(voucherId) {
      return this.vouchersStore.getVoucherQuantity(voucherId)
    },
    increaseQuantity(voucherId) {
      const voucher = this.vouchers.find(v => v.id === voucherId)
      const currentQuantity = this.getQuantity(voucherId)
      
      if (currentQuantity >= voucher.maxQuantity) {
        this.warning(`Maximum quantity for ${voucher.name} is ${voucher.maxQuantity}`)
        return
      }
      
      this.vouchersStore.increaseQuantity(voucherId)
    },
    decreaseQuantity(voucherId) {
      this.vouchersStore.decreaseQuantity(voucherId)
    },
    updateQuantity(voucherId, value) {
      const quantity = parseInt(value) || 0
      this.vouchersStore.updateVoucherQuantity(voucherId, quantity)
    },
    validateQuantity(voucherId, value) {
      const voucher = this.vouchers.find(v => v.id === voucherId)
      const quantity = parseInt(value) || 0
      
      if (quantity < 0) {
        this.vouchersStore.updateVoucherQuantity(voucherId, 0)
        this.warning('Quantity cannot be negative')
      } else if (quantity > voucher.maxQuantity) {
        this.vouchersStore.updateVoucherQuantity(voucherId, voucher.maxQuantity)
        this.warning(`Maximum quantity for ${voucher.name} is ${voucher.maxQuantity}`)
      }
    },
    resetSelections() {
      this.vouchersStore.resetSelection()
    }
  }
}
</script>
