<template>
  <button
    :type="type"
    :disabled="disabled || loading"
    :class="buttonClasses"
    @click="handleClick"
  >
    <svg
      v-if="loading"
      class="animate-spin -ml-1 mr-2 h-4 w-4"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        class="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        stroke-width="4"
      ></circle>
      <path
        class="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      ></path>
    </svg>
    
    <component
      v-if="icon && !loading"
      :is="icon"
      :class="iconClasses"
    />
    
    <slot />
  </button>
</template>

<script>
export default {
  name: 'BaseButton',
  props: {
    variant: {
      type: String,
      default: 'primary',
      validator: (value) => ['primary', 'secondary', 'danger', 'success', 'outline'].includes(value)
    },
    size: {
      type: String,
      default: 'md',
      validator: (value) => ['sm', 'md', 'lg'].includes(value)
    },
    type: {
      type: String,
      default: 'button'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    icon: {
      type: [String, Object],
      default: null
    },
    iconPosition: {
      type: String,
      default: 'left',
      validator: (value) => ['left', 'right'].includes(value)
    }
  },
  emits: ['click'],
  computed: {
    buttonClasses() {
      const baseClasses = [
        'inline-flex',
        'items-center',
        'justify-center',
        'font-medium',
        'rounded-lg',
        'transition-colors',
        'duration-200',
        'focus:outline-none',
        'focus:ring-2',
        'focus:ring-offset-2',
        'disabled:opacity-50',
        'disabled:cursor-not-allowed'
      ]

      // Size classes
      const sizeClasses = {
        sm: ['px-3', 'py-1.5', 'text-sm'],
        md: ['px-4', 'py-2', 'text-sm'],
        lg: ['px-6', 'py-3', 'text-base']
      }

      // Variant classes
      const variantClasses = {
        primary: [
          'bg-primary-600',
          'text-white',
          'hover:bg-primary-700',
          'focus:ring-primary-500'
        ],
        secondary: [
          'bg-secondary-100',
          'text-secondary-800',
          'hover:bg-secondary-200',
          'focus:ring-secondary-500'
        ],
        danger: [
          'bg-red-600',
          'text-white',
          'hover:bg-red-700',
          'focus:ring-red-500'
        ],
        success: [
          'bg-green-600',
          'text-white',
          'hover:bg-green-700',
          'focus:ring-green-500'
        ],
        outline: [
          'border',
          'border-gray-300',
          'bg-white',
          'text-gray-700',
          'hover:bg-gray-50',
          'focus:ring-primary-500'
        ]
      }

      return [
        ...baseClasses,
        ...sizeClasses[this.size],
        ...variantClasses[this.variant]
      ]
    },
    iconClasses() {
      const baseClasses = ['h-4', 'w-4']
      
      if (this.$slots.default) {
        if (this.iconPosition === 'left') {
          baseClasses.push('mr-2')
        } else {
          baseClasses.push('ml-2')
        }
      }
      
      return baseClasses
    }
  },
  methods: {
    handleClick(event) {
      if (!this.disabled && !this.loading) {
        this.$emit('click', event)
      }
    }
  }
}
</script>
