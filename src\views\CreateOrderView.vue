<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900">Create Order</h1>
      <BaseButton
        variant="secondary"
        @click="$router.push('/orders')"
      >
        <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        Back to Orders
      </BaseButton>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- List Voucher Digital -->
      <div class="lg:col-span-2">
        <VoucherList />
      </div>
      
      <!-- Order Summary -->
      <div class="lg:col-span-1">
        <OrderSummary @place-order="handlePlaceOrder" />
      </div>
    </div>

    <!-- Success Modal -->
    <SuccessModal
      :is-open="showSuccessModal"
      :order-data="orderData"
      @close="handleModalClose"
    />
  </div>
</template>

<script>
import { useVouchersStore } from '@/stores/vouchers'
import { useOrdersStore } from '@/stores/orders'
import { useNotification } from '@/composables/useNotification'
import BaseButton from '@/components/base/BaseButton.vue'
import VoucherList from '@/components/order/VoucherList.vue'
import OrderSummary from '@/components/order/OrderSummary.vue'
import SuccessModal from '@/components/order/SuccessModal.vue'

export default {
  name: 'CreateOrderView',
  components: {
    BaseButton,
    VoucherList,
    OrderSummary,
    SuccessModal
  },
  setup() {
    const vouchersStore = useVouchersStore()
    const ordersStore = useOrdersStore()
    const { success, error } = useNotification()

    return {
      vouchersStore,
      ordersStore,
      success,
      error
    }
  },
  data() {
    return {
      showSuccessModal: false,
      orderData: null
    }
  },
  async mounted() {
    // Ensure modal is closed on mount
    this.showSuccessModal = false
    this.orderData = null

    // Fetch vouchers when component mounts
    await this.vouchersStore.fetchVouchers()

    // Reset any previous selections
    this.vouchersStore.resetSelection()
  },
  methods: {
    async handlePlaceOrder() {
      try {
        if (!this.vouchersStore.hasSelectedVouchers) {
          this.error('Please select at least one voucher before placing an order')
          return
        }

        const orderData = this.vouchersStore.createOrderData()
        const newOrder = await this.ordersStore.addOrder(orderData)

        // Show success modal with order details
        this.orderData = newOrder
        this.showSuccessModal = true
        this.success(`Order created successfully! Order ID: ${newOrder.orderId}`)
        
        // Reset voucher selections
        this.vouchersStore.resetSelection()
        
      } catch (err) {
        this.error('Failed to create order. Please try again.')
      }
    },
    handleModalClose() {
      this.showSuccessModal = false
      this.orderData = null
      // Navigate back to orders list
      this.$router.push('/orders')
    }
  }
}
</script>
