/**
 * Format currency in Indonesian Rupiah
 * @param {number} amount - The amount to format
 * @returns {string} Formatted currency string
 */
export function formatCurrency(amount) {
  if (typeof amount !== 'number') {
    return 'Rp 0,-'
  }
  
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount).replace('IDR', 'Rp') + ',-'
}

/**
 * Get status badge color class
 * @param {string} status - The status
 * @returns {string} CSS class for status badge
 */
export function getStatusBadgeClass(status) {
  const statusClasses = {
    'Waiting for Payment': 'bg-yellow-100 text-yellow-800',
    'On Payment Verification': 'bg-secondary-100 text-secondary-800',
    'On Release Progress': 'bg-primary-100 text-primary-800',
    'Payment Rejected': 'bg-red-100 text-red-800',
    'Done': 'bg-green-100 text-green-800'
  }

  return statusClasses[status] || 'bg-accent-100 text-accent-800'
}
