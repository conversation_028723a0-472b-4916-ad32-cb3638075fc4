<template>
  <!-- Modal Overlay -->
  <div
    v-show="isOpen"
    class="fixed inset-0 z-50 overflow-y-auto"
    @click="handleOverlayClick"
  >
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div
        v-show="isOpen"
        class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
        @click="$emit('close')"
      ></div>

      <!-- Modal panel -->
      <div
        v-show="isOpen"
        class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6"
        @click.stop
      >
        <!-- Success Icon -->
        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
          <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
        </div>

        <!-- Content -->
        <div class="text-center">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-2">
            Order successfully created!
          </h3>
          
          <div v-if="orderData && isOpen" class="mt-4 text-left bg-gray-50 rounded-lg p-4">
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-500">Order ID:</span>
                <span class="font-medium text-gray-900">{{ orderData.orderId }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">Order Date:</span>
                <span class="font-medium text-gray-900">{{ orderData.orderDate }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">Status:</span>
                <span
                  :class="[
                    'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                    getStatusBadgeClass(orderData.status)
                  ]"
                >
                  {{ orderData.status }}
                </span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">Total:</span>
                <span class="font-bold text-primary-600">{{ formatCurrency(orderData.total) }}</span>
              </div>
            </div>
            
            <!-- Voucher Details -->
            <div v-if="orderData.vouchers && orderData.vouchers.length > 0" class="mt-4">
              <h4 class="text-sm font-medium text-gray-900 mb-2">Vouchers Ordered:</h4>
              <div class="space-y-1">
                <div
                  v-for="voucher in orderData.vouchers"
                  :key="voucher.id"
                  class="flex justify-between text-xs text-gray-600"
                >
                  <span>{{ voucher.name }} ({{ voucher.quantity }}x)</span>
                  <span>{{ formatCurrency(voucher.totalPrice) }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <p class="mt-4 text-sm text-gray-500">
            Your order has been created and is now waiting for payment. You can track the status in the orders list.
          </p>
        </div>

        <!-- Actions -->
        <div class="mt-6 flex flex-col sm:flex-row sm:space-x-3 space-y-3 sm:space-y-0">
          <BaseButton
            variant="primary"
            class="flex-1"
            @click="$emit('close')"
          >
            OK
          </BaseButton>
          
          <BaseButton
            variant="outline"
            class="flex-1"
            @click="createAnotherOrder"
          >
            Create Another Order
          </BaseButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { formatCurrency, getStatusBadgeClass } from '@/utils/formatters'
import BaseButton from '@/components/base/BaseButton.vue'

export default {
  name: 'SuccessModal',
  components: {
    BaseButton
  },
  props: {
    isOpen: {
      type: Boolean,
      default: false
    },
    orderData: {
      type: Object,
      default: null
    }
  },
  emits: ['close'],
  setup() {
    return {
      formatCurrency,
      getStatusBadgeClass
    }
  },
  methods: {
    handleOverlayClick(event) {
      if (event.target === event.currentTarget) {
        this.$emit('close')
      }
    },
    createAnotherOrder() {
      this.$emit('close')
      // The parent component will handle navigation
      // We just close the modal and let the parent decide
    }
  }
}
</script>
