# Superindo Vue 3 - Order Management System

A modern, responsive order management system built with Vue 3, Vite, and Tailwind CSS for Superindo digital voucher orders.

## 🚀 Features

- **Modern Vue 3 Architecture**: Built with Vue 3 Composition API and script setup syntax
- **Responsive Design**: Mobile-first design using Tailwind CSS
- **Order Management**: Complete order lifecycle management with status tracking
- **Digital Voucher System**: Manage and order digital vouchers with quantity controls
- **Professional UI**: Clean, modern interface with consistent design patterns
- **Error Handling**: Comprehensive error handling and user feedback

## 🛠️ Tech Stack

- **Frontend Framework**: Vue 3 (Composition API)
- **Build Tool**: Vite 7.0.5
- **Styling**: Tailwind CSS 3.4.0
- **State Management**: Pinia 2.3.1
- **Routing**: Vue Router 4.5.0
- **Code Quality**: ESLint + Prettier
- **Package Manager**: NPM

## 📋 Requirements

- Node.js 20.16.0 or higher
- NPM 10.8.1 or higher

## 🚀 Quick Start

### 1. Clone the repository
```bash
git clone <repository-url>
cd superindo-vue-3
```

### 2. Install dependencies
```bash
npm install
```

### 3. Start development server
```bash
npm run dev
```

The application will be available at `http://localhost:5174`

## 📁 Project Structure

```
src/
├── assets/              # Static assets and global styles
├── components/          # Reusable Vue components
│   ├── base/           # Base UI components (Button, Input, Select)
│   ├── layout/         # Layout components (Header, Sidebar, Layout)
│   ├── order/          # Order-specific components
│   └── ui/             # UI utility components
├── composables/        # Vue composables for reusable logic
├── data/              # JSON data files
├── router/            # Vue Router configuration
├── stores/            # Pinia stores for state management
├── utils/             # Utility functions
├── views/             # Page components
└── main.js            # Application entry point
```

## 🎯 Core Features

### Order List Page
- Filter orders by Order ID, Status, and Order Date
- Display orders in a responsive data table
- Status badges with color coding
- Create new order button

### Create Order Page
- List of available digital vouchers
- Quantity controls with validation
- Real-time order summary calculation
- Success modal with order confirmation

### Dashboard
- Overview statistics
- Quick action buttons
- Recent orders display

### Voucher Management
- View all available vouchers
- Voucher status management
- Responsive card layout

## 🔧 Available Scripts

```bash
# Development
npm run dev          # Start development server

# Production
npm run build        # Build for production
npm run preview      # Preview production build

# Code Quality
npm run lint         # Run ESLint
npm run format       # Format code with Prettier
```

## 📊 Data Structure

### Order Object
```javascript
{
  "orderId": "BLK0123456123",
  "orderDate": "28 Jun 2024, 10:30",
  "status": "Waiting for Payment",
  "total": 15000000,
  "vouchers": [
    {
      "id": 55,
      "name": "Voucher Digital Superindo 50K",
      "denomination": 50000,
      "quantity": 20,
      "totalPrice": 1000000
    }
  ]
}
```

### Voucher Object
```javascript
{
  "id": 55,
  "name": "Voucher Digital Superindo 50K",
  "denomination": 50000,
  "code": "55",
  "description": "Digital voucher for Superindo with 50,000 IDR value",
  "isActive": true,
  "maxQuantity": 100
}
```

## 🎨 Design System

### Colors
- **Primary**: Blue (#3b82f6)
- **Success**: Green (#10b981)
- **Warning**: Yellow (#f59e0b)
- **Error**: Red (#ef4444)
- **Gray Scale**: Various shades for text and backgrounds

### Typography
- **Font Family**: Inter (Google Fonts)
- **Font Weights**: 300, 400, 500, 600, 700

### Components
- Consistent spacing using Tailwind's spacing scale
- Rounded corners (8px default)
- Shadow system for depth
- Hover and focus states for interactivity

## 🔒 Status Types

- **Waiting for Payment**: Order created, awaiting payment
- **On Payment Verification**: Payment received, being verified
- **On Release Progress**: Payment verified, vouchers being processed
- **Payment Rejected**: Payment was rejected
- **Done**: Order completed successfully

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

The built files will be in the `dist/` directory, ready for deployment to any static hosting service.

### Environment Variables
Create a `.env` file for environment-specific configuration:
```env
VITE_API_BASE_URL=https://api.example.com
VITE_APP_TITLE=Superindo Order Management
```

## 📝 Code Style

This project uses ESLint and Prettier for code formatting and quality:

- **ESLint**: Enforces code quality rules
- **Prettier**: Handles code formatting
- **Vue Style Guide**: Follows Vue.js official style guide

## 🐛 Troubleshooting

### Common Issues

1. **Port already in use**: Vite will automatically try the next available port
2. **Node version**: Ensure you're using Node.js 20.16.0 or higher
3. **Dependencies**: Run `npm install` if you encounter module errors

### Performance Tips

- Use `npm run build` for production builds
- Enable gzip compression on your server
- Use a CDN for static assets

